% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
%
\documentclass[
]{article}
\usepackage{amsmath,amssymb}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{xcolor}
\usepackage[margin=1in]{geometry}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\usepackage{graphicx}
\usepackage{float}
\usepackage{tikz}
\usepackage{xcolor}
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\author{}
\date{\vspace{-2.5em}}

\begin{document}

\section{Question}\label{question}

Si la mediana de la asistencia a las cinco salas de cine de un centro
comercial es 487 y no hay dos salas de cine con el mismo número de
asistentes, ¿cuál es el mayor valor posible para ``N''?

\includegraphics[width=8cm,height=\textheight,keepaspectratio]{tabla_datos.png}

\subsection{Answerlist}\label{answerlist}

\begin{itemize}
\tightlist
\item
  480
\item
  486
\item
  494
\item
  487
\end{itemize}

\section{Solution}\label{solution}

\subsubsection{Análisis del problema}\label{analisis-problema-6425}

Para resolver este problema, necesitamos entender qué significa que la
mediana sea 487 en un conjunto de 5 valores.

\textbf{Datos conocidos:}

\begin{itemize}
\tightlist
\item
  Tenemos 5 salas de cine: C, B, A, D, E
\item
  Sus valores son: 578, 497, 436, 487, N
\item
  La mediana del conjunto es 487
\item
  No hay dos valores iguales
\end{itemize}

\subsubsection{Concepto de mediana}\label{concepto-mediana-9086}

La mediana de 5 valores ordenados de menor a mayor es el valor que ocupa
la \textbf{posición central (posición 3)}.

Para que la mediana sea 487, cuando ordenemos los 5 valores de menor a
mayor, el valor en la posición 3 debe ser exactamente 487.

\subsubsection{Análisis de casos}\label{analisis-casos-992}

Los valores conocidos son: 436, 497, 578, 487

Analicemos dónde puede ubicarse N para que la mediana sea 487:

\hfill\break

\textbf{Caso 1:} Si N es menor o igual a 436 (muy pequeño) - Orden: N,
436, 487, 497, 578 - Mediana = 487 diferente de 487

\textbf{Caso 2:} Si 436 \textless{} N \textless{} 487 - El valor 487
queda en posición 3 - Mediana = 487 (CORRECTO)

\textbf{Caso 3:} Si N = 487 - No es válido porque no puede haber valores
iguales

\textbf{Caso 4:} Si N \textgreater{} 487 - El valor 487 no estaría en
posición 3 - Mediana diferente de 487

\subsubsection{Conclusión}\label{conclusion-9582}

Para que la mediana sea 487 , N debe cumplir: 436 \textless{} N
\textless{} 487

El \textbf{mayor valor posible} para N es \textbf{486} (justo menor que
487).

\subsection{Answerlist}\label{answerlist-1}

\begin{itemize}
\tightlist
\item
  Falso
\item
  Verdadero
\item
  Falso
\item
  Falso
\end{itemize}

\section{Meta-information}\label{meta-information}

exname: mediana\_asistencia\_salas\_cine extype: schoice exsolution:
0100 exshuffle: TRUE exsection: Estadística\textbar Medidas de
posición\textbar Mediana\textbar Interpretación exextra{[}Type{]}:
Interpretación y representación exextra{[}Level{]}: 2
exextra{[}Language{]}: es exextra{[}Course{]}: Matemáticas ICFES

\end{document}
